<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Booking - Encuentra tu próxima estancia</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #003580, #0057b8);
            color: white;
            padding: 20px 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .nav-links a:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .hero {
            text-align: center;
            margin-bottom: 40px;
        }

        .hero h1 {
            font-size: 48px;
            margin-bottom: 10px;
        }

        .hero p {
            font-size: 18px;
            opacity: 0.9;
        }

        /* Search Form */
        .search-form {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .search-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-field {
            flex: 1;
            min-width: 200px;
        }

        .search-field label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }

        .search-field input,
        .search-field select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .search-field input:focus,
        .search-field select:focus {
            outline: none;
            border-color: #0057b8;
        }

        .search-btn {
            background: #0057b8;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-top: 20px;
        }

        .search-btn:hover {
            background: #003580;
        }

        /* Main Content */
        .main-content {
            display: flex;
            gap: 30px;
            margin-top: 30px;
        }

        /* Filters Sidebar */
        .filters {
            width: 300px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            height: fit-content;
        }

        .filters h3 {
            margin-bottom: 20px;
            color: #333;
            border-bottom: 2px solid #0057b8;
            padding-bottom: 10px;
        }

        .filter-group {
            margin-bottom: 25px;
        }

        .filter-group h4 {
            margin-bottom: 15px;
            color: #555;
            font-size: 16px;
        }

        .filter-option {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 5px 0;
        }

        .filter-option label {
            display: flex;
            align-items: center;
            cursor: pointer;
            flex: 1;
        }

        .filter-option input[type="checkbox"] {
            margin-right: 10px;
        }

        .filter-count {
            color: #666;
            font-size: 12px;
        }

        /* Results */
        .results {
            flex: 1;
        }

        .results-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .results-count {
            font-size: 18px;
            font-weight: 500;
        }

        .sort-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .accommodation-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .accommodation-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .card-content {
            display: flex;
            padding: 20px;
            gap: 20px;
        }

        .card-image {
            width: 200px;
            height: 150px;
            border-radius: 8px;
            object-fit: cover;
        }

        .card-info {
            flex: 1;
        }

        .card-title {
            font-size: 20px;
            font-weight: 600;
            color: #0057b8;
            margin-bottom: 8px;
        }

        .card-location {
            color: #666;
            margin-bottom: 10px;
        }

        .card-description {
            color: #333;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .card-amenities {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .amenity {
            background: #e8f4fd;
            color: #0057b8;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        .card-rating {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .rating-score {
            background: #0057b8;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: 600;
        }

        .rating-text {
            color: #666;
            font-size: 14px;
        }

        .card-price {
            text-align: right;
            padding: 20px;
            border-left: 1px solid #eee;
        }

        .price-amount {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .price-period {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
        }

        .book-btn {
            background: #0057b8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.3s;
        }

        .book-btn:hover {
            background: #003580;
        }

        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }

            .filters {
                width: 100%;
            }

            .search-row {
                flex-direction: column;
            }

            .card-content {
                flex-direction: column;
            }

            .card-image {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="nav">
                <div class="logo">Booking.com</div>
                <div class="nav-links">
                    <a href="#">Alojamientos</a>
                    <a href="#">Vuelos</a>
                    <a href="#">Alquiler de coches</a>
                    <a href="#">Atracciones</a>
                    <a href="#">Taxi aeropuerto</a>
                </div>
            </nav>

            <div class="hero">
                <h1>Encuentra tu próxima estancia</h1>
                <p>Busca ofertas en hoteles, casas y mucho más...</p>
            </div>

            <!-- Search Form -->
            <form class="search-form" id="searchForm">
                <div class="search-row">
                    <div class="search-field">
                        <label for="destination">Destino</label>
                        <input type="text" id="destination" placeholder="¿A dónde vas?" value="Guatapé">
                    </div>
                    <div class="search-field">
                        <label for="checkin">Fecha de entrada</label>
                        <input type="date" id="checkin">
                    </div>
                    <div class="search-field">
                        <label for="checkout">Fecha de salida</label>
                        <input type="date" id="checkout">
                    </div>
                    <div class="search-field">
                        <label for="guests">Huéspedes</label>
                        <select id="guests">
                            <option value="1">1 adulto</option>
                            <option value="2" selected>2 adultos</option>
                            <option value="3">3 adultos</option>
                            <option value="4">4 adultos</option>
                        </select>
                    </div>
                </div>
                <button type="submit" class="search-btn">Buscar</button>
            </form>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container">
        <div class="main-content">
            <!-- Filters Sidebar -->
            <aside class="filters">
                <h3>Filtrar por:</h3>

                <div class="filter-group">
                    <h4>Filtros populares</h4>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="filter" value="piscina">
                            Piscina
                        </label>
                        <span class="filter-count">31</span>
                    </div>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="filter" value="wifi">
                            WiFi gratis
                        </label>
                        <span class="filter-count">219</span>
                    </div>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="filter" value="casas-chalets">
                            Casas y chalets
                        </label>
                        <span class="filter-count">24</span>
                    </div>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="filter" value="villas">
                            Villas
                        </label>
                        <span class="filter-count">13</span>
                    </div>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="filter" value="tv">
                            TV
                        </label>
                        <span class="filter-count">139</span>
                    </div>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="filter" value="apartamentos">
                            Apartamentos
                        </label>
                        <span class="filter-count">93</span>
                    </div>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="filter" value="parking">
                            Parking
                        </label>
                        <span class="filter-count">172</span>
                    </div>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="filter" value="desayuno">
                            Bed and breakfast
                        </label>
                        <span class="filter-count">3</span>
                    </div>
                </div>

                <div class="filter-group">
                    <h4>Tipo de alojamiento</h4>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="type" value="apartamentos">
                            Apartamentos
                        </label>
                        <span class="filter-count">93</span>
                    </div>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="type" value="hoteles">
                            Hoteles
                        </label>
                        <span class="filter-count">58</span>
                    </div>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="type" value="casas-chalets">
                            Casas y chalets
                        </label>
                        <span class="filter-count">24</span>
                    </div>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="type" value="villas">
                            Villas
                        </label>
                        <span class="filter-count">13</span>
                    </div>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="type" value="campings">
                            Campings
                        </label>
                        <span class="filter-count">12</span>
                    </div>
                </div>
            </aside>

            <!-- Results Section -->
            <main class="results">
                <div class="results-header">
                    <div class="results-count">Guatapé: <span id="resultsCount">235</span> alojamientos encontrados</div>
                    <select class="sort-select" id="sortSelect">
                        <option value="recommended">Ordenar por: Nuestros destacados</option>
                        <option value="price-low">Precio: de menor a mayor</option>
                        <option value="price-high">Precio: de mayor a menor</option>
                        <option value="rating">Puntuación de los huéspedes</option>
                        <option value="distance">Distancia al centro</option>
                    </select>
                </div>

                <div id="accommodationsList">
                    <!-- Hotel Zocalo Campestre -->
                    <div class="accommodation-card" data-type="hoteles" data-amenities="wifi,piscina,parking">
                        <div class="card-content">
                            <img src="https://images.unsplash.com/photo-1566073771259-6a8506099945?w=200&h=150&fit=crop" alt="Hotel Zocalo Campestre" class="card-image">
                            <div class="card-info">
                                <h3 class="card-title">Hotel Zocalo Campestre</h3>
                                <p class="card-location">Guatapé • Mostrar en el mapa • a 2.1 km del centro</p>
                                <p class="card-description">El Hotel Zocalo Campestre se encuentra en Guatapé, a 70 km de Medellín. Ofrece restaurante, WiFi gratuita y bar. Este establecimiento dispone de habitaciones en la zona principal y bungalows.</p>
                                <div class="card-amenities">
                                    <span class="amenity">WiFi gratis</span>
                                    <span class="amenity">Piscina</span>
                                    <span class="amenity">Parking</span>
                                </div>
                                <div class="card-rating">
                                    <span class="rating-score">9.2</span>
                                    <span class="rating-text">Fantástico • 478 comentarios</span>
                                </div>
                            </div>
                            <div class="card-price">
                                <div class="price-amount">$85.000</div>
                                <div class="price-period">por noche</div>
                                <button class="book-btn">Mostrar precios</button>
                            </div>
                        </div>
                    </div>

                    <!-- Hotel Verony Guatape -->
                    <div class="accommodation-card" data-type="hoteles" data-amenities="wifi,piscina,parking,tv">
                        <div class="card-content">
                            <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=200&h=150&fit=crop" alt="Hotel Verony Guatape" class="card-image">
                            <div class="card-info">
                                <h3 class="card-title">Hotel Verony Guatape</h3>
                                <p class="card-location">Guatapé • Mostrar en el mapa • a 1.8 km del centro</p>
                                <p class="card-description">Hotel Verony Guatape está en Guatapé, a 12 min a pie de Piedra del Peñol, y dispone de alojamiento con piscina al aire libre, parking gratuito privado, jardín y salón de uso común.</p>
                                <div class="card-amenities">
                                    <span class="amenity">WiFi gratis</span>
                                    <span class="amenity">Piscina</span>
                                    <span class="amenity">Parking gratis</span>
                                    <span class="amenity">TV</span>
                                </div>
                                <div class="card-rating">
                                    <span class="rating-score">9.2</span>
                                    <span class="rating-text">Muy bien • 1.234 comentarios</span>
                                </div>
                            </div>
                            <div class="card-price">
                                <div class="price-amount">$95.000</div>
                                <div class="price-period">por noche</div>
                                <button class="book-btn">Mostrar precios</button>
                            </div>
                        </div>
                    </div>

                    <!-- Hotel Los Recuerdos -->
                    <div class="accommodation-card" data-type="hoteles" data-amenities="wifi,parking,tv">
                        <div class="card-content">
                            <img src="https://images.unsplash.com/photo-1564501049412-61c2a3083791?w=200&h=150&fit=crop" alt="Hotel Los Recuerdos" class="card-image">
                            <div class="card-info">
                                <h3 class="card-title">Hotel Los Recuerdos</h3>
                                <p class="card-location">Guatapé • Mostrar en el mapa • a 2.4 km del centro</p>
                                <p class="card-description">El Hotel Los Recuerdos está situado en el centro de Guatapé y ofrece vistas espléndidas de El Peñol, una gran alberca climatizada al aire libre con servicio de bar de bebidas. La WiFi es gratuita.</p>
                                <div class="card-amenities">
                                    <span class="amenity">WiFi gratis</span>
                                    <span class="amenity">Parking</span>
                                    <span class="amenity">TV</span>
                                    <span class="amenity">Bar</span>
                                </div>
                                <div class="card-rating">
                                    <span class="rating-score">8.9</span>
                                    <span class="rating-text">Muy bien • 845 comentarios</span>
                                </div>
                            </div>
                            <div class="card-price">
                                <div class="price-amount">$75.000</div>
                                <div class="price-period">por noche</div>
                                <button class="book-btn">Mostrar precios</button>
                            </div>
                        </div>
                    </div>

                    <!-- Casa Rural El Mirador -->
                    <div class="accommodation-card" data-type="casas-chalets" data-amenities="wifi,piscina,parking">
                        <div class="card-content">
                            <img src="https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=200&h=150&fit=crop" alt="Casa Rural El Mirador" class="card-image">
                            <div class="card-info">
                                <h3 class="card-title">Casa Rural El Mirador</h3>
                                <p class="card-location">Guatapé • Mostrar en el mapa • a 3.2 km del centro</p>
                                <p class="card-description">Hermosa casa rural con vista panorámica al embalse. Perfecta para familias, cuenta con piscina privada, jardín amplio y todas las comodidades para una estancia inolvidable.</p>
                                <div class="card-amenities">
                                    <span class="amenity">WiFi gratis</span>
                                    <span class="amenity">Piscina privada</span>
                                    <span class="amenity">Parking</span>
                                    <span class="amenity">Jardín</span>
                                </div>
                                <div class="card-rating">
                                    <span class="rating-score">9.5</span>
                                    <span class="rating-text">Excepcional • 156 comentarios</span>
                                </div>
                            </div>
                            <div class="card-price">
                                <div class="price-amount">$120.000</div>
                                <div class="price-period">por noche</div>
                                <button class="book-btn">Mostrar precios</button>
                            </div>
                        </div>
                    </div>

                    <!-- Apartamento Vista Lago -->
                    <div class="accommodation-card" data-type="apartamentos" data-amenities="wifi,tv,parking">
                        <div class="card-content">
                            <img src="https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=200&h=150&fit=crop" alt="Apartamento Vista Lago" class="card-image">
                            <div class="card-info">
                                <h3 class="card-title">Apartamento Vista Lago</h3>
                                <p class="card-location">Guatapé • Mostrar en el mapa • a 1.5 km del centro</p>
                                <p class="card-description">Moderno apartamento con vista directa al lago. Completamente equipado con cocina, sala de estar y balcón privado. Ideal para parejas o familias pequeñas.</p>
                                <div class="card-amenities">
                                    <span class="amenity">WiFi gratis</span>
                                    <span class="amenity">TV</span>
                                    <span class="amenity">Cocina</span>
                                    <span class="amenity">Balcón</span>
                                </div>
                                <div class="card-rating">
                                    <span class="rating-score">8.7</span>
                                    <span class="rating-text">Muy bien • 298 comentarios</span>
                                </div>
                            </div>
                            <div class="card-price">
                                <div class="price-amount">$65.000</div>
                                <div class="price-period">por noche</div>
                                <button class="book-btn">Mostrar precios</button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Datos de ejemplo para los alojamientos
        const accommodations = [
            {
                id: 1,
                name: "Hotel Zocalo Campestre",
                type: "hoteles",
                location: "Guatapé",
                distance: "2.1 km del centro",
                description: "El Hotel Zocalo Campestre se encuentra en Guatapé, a 70 km de Medellín. Ofrece restaurante, WiFi gratuita y bar.",
                amenities: ["wifi", "piscina", "parking"],
                rating: 9.2,
                reviews: 478,
                price: 85000,
                image: "https://images.unsplash.com/photo-1566073771259-6a8506099945?w=200&h=150&fit=crop"
            },
            {
                id: 2,
                name: "Hotel Verony Guatape",
                type: "hoteles",
                location: "Guatapé",
                distance: "1.8 km del centro",
                description: "Hotel Verony Guatape está en Guatapé, a 12 min a pie de Piedra del Peñol, y dispone de alojamiento con piscina al aire libre.",
                amenities: ["wifi", "piscina", "parking", "tv"],
                rating: 9.2,
                reviews: 1234,
                price: 95000,
                image: "https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=200&h=150&fit=crop"
            },
            {
                id: 3,
                name: "Hotel Los Recuerdos",
                type: "hoteles",
                location: "Guatapé",
                distance: "2.4 km del centro",
                description: "El Hotel Los Recuerdos está situado en el centro de Guatapé y ofrece vistas espléndidas de El Peñol.",
                amenities: ["wifi", "parking", "tv"],
                rating: 8.9,
                reviews: 845,
                price: 75000,
                image: "https://images.unsplash.com/photo-1564501049412-61c2a3083791?w=200&h=150&fit=crop"
            },
            {
                id: 4,
                name: "Casa Rural El Mirador",
                type: "casas-chalets",
                location: "Guatapé",
                distance: "3.2 km del centro",
                description: "Hermosa casa rural con vista panorámica al embalse. Perfecta para familias, cuenta con piscina privada.",
                amenities: ["wifi", "piscina", "parking"],
                rating: 9.5,
                reviews: 156,
                price: 120000,
                image: "https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=200&h=150&fit=crop"
            },
            {
                id: 5,
                name: "Apartamento Vista Lago",
                type: "apartamentos",
                location: "Guatapé",
                distance: "1.5 km del centro",
                description: "Moderno apartamento con vista directa al lago. Completamente equipado con cocina, sala de estar y balcón privado.",
                amenities: ["wifi", "tv", "parking"],
                rating: 8.7,
                reviews: 298,
                price: 65000,
                image: "https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=200&h=150&fit=crop"
            }
        ];

        let filteredAccommodations = [...accommodations];

        // Función para renderizar las tarjetas de alojamiento
        function renderAccommodations(accommodationsList) {
            const container = document.getElementById('accommodationsList');
            container.innerHTML = '';

            accommodationsList.forEach(accommodation => {
                const amenityTags = accommodation.amenities.map(amenity => {
                    const amenityNames = {
                        'wifi': 'WiFi gratis',
                        'piscina': 'Piscina',
                        'parking': 'Parking',
                        'tv': 'TV',
                        'desayuno': 'Desayuno'
                    };
                    return `<span class="amenity">${amenityNames[amenity] || amenity}</span>`;
                }).join('');

                const card = `
                    <div class="accommodation-card" data-type="${accommodation.type}" data-amenities="${accommodation.amenities.join(',')}">
                        <div class="card-content">
                            <img src="${accommodation.image}" alt="${accommodation.name}" class="card-image">
                            <div class="card-info">
                                <h3 class="card-title">${accommodation.name}</h3>
                                <p class="card-location">${accommodation.location} • Mostrar en el mapa • a ${accommodation.distance}</p>
                                <p class="card-description">${accommodation.description}</p>
                                <div class="card-amenities">
                                    ${amenityTags}
                                </div>
                                <div class="card-rating">
                                    <span class="rating-score">${accommodation.rating}</span>
                                    <span class="rating-text">Muy bien • ${accommodation.reviews} comentarios</span>
                                </div>
                            </div>
                            <div class="card-price">
                                <div class="price-amount">$${accommodation.price.toLocaleString()}</div>
                                <div class="price-period">por noche</div>
                                <button class="book-btn">Mostrar precios</button>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += card;
            });

            // Actualizar contador de resultados
            document.getElementById('resultsCount').textContent = accommodationsList.length;
        }

        // Función para aplicar filtros
        function applyFilters() {
            const checkedFilters = Array.from(document.querySelectorAll('input[type="checkbox"]:checked'))
                .map(checkbox => checkbox.value);

            if (checkedFilters.length === 0) {
                filteredAccommodations = [...accommodations];
            } else {
                filteredAccommodations = accommodations.filter(accommodation => {
                    // Verificar si el alojamiento coincide con algún filtro de tipo
                    const typeMatch = checkedFilters.some(filter => accommodation.type === filter);

                    // Verificar si el alojamiento tiene alguna de las amenidades seleccionadas
                    const amenityMatch = checkedFilters.some(filter => accommodation.amenities.includes(filter));

                    return typeMatch || amenityMatch;
                });
            }

            renderAccommodations(filteredAccommodations);
        }

        // Función para ordenar resultados
        function sortResults(sortBy) {
            let sorted = [...filteredAccommodations];

            switch (sortBy) {
                case 'price-low':
                    sorted.sort((a, b) => a.price - b.price);
                    break;
                case 'price-high':
                    sorted.sort((a, b) => b.price - a.price);
                    break;
                case 'rating':
                    sorted.sort((a, b) => b.rating - a.rating);
                    break;
                case 'distance':
                    sorted.sort((a, b) => parseFloat(a.distance) - parseFloat(b.distance));
                    break;
                default:
                    // Mantener orden recomendado (por defecto)
                    break;
            }

            renderAccommodations(sorted);
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Renderizar alojamientos iniciales
            renderAccommodations(accommodations);

            // Configurar fechas por defecto
            const today = new Date();
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);

            document.getElementById('checkin').value = today.toISOString().split('T')[0];
            document.getElementById('checkout').value = tomorrow.toISOString().split('T')[0];

            // Event listeners para filtros
            document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
                checkbox.addEventListener('change', applyFilters);
            });

            // Event listener para ordenamiento
            document.getElementById('sortSelect').addEventListener('change', function() {
                sortResults(this.value);
            });

            // Event listener para formulario de búsqueda
            document.getElementById('searchForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const destination = document.getElementById('destination').value;
                const checkin = document.getElementById('checkin').value;
                const checkout = document.getElementById('checkout').value;
                const guests = document.getElementById('guests').value;

                // Aquí podrías implementar la lógica de búsqueda real
                console.log('Búsqueda:', { destination, checkin, checkout, guests });

                // Simular nueva búsqueda
                alert(`Buscando alojamientos en ${destination} para ${guests} huéspedes del ${checkin} al ${checkout}`);
            });

            // Event listeners para botones de reserva
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('book-btn')) {
                    const card = e.target.closest('.accommodation-card');
                    const title = card.querySelector('.card-title').textContent;
                    alert(`Redirigiendo a la página de reserva para: ${title}`);
                }
            });
        });
    </script>
</body>
</html>